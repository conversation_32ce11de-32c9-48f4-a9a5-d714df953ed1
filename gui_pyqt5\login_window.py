"""
PyQt5 Login Window

This module contains the PyQt5 implementation of the login window,
migrated from the original Tkinter version while preserving all
functionality and user experience.
"""

import os
import sys
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QFrame, QCheckBox, QSizePolicy
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QIcon

# Import utilities and base components
from .utils import (
    center_window, show_info_message, show_error_message,
    ensure_application, create_font
)
from .base_components import StyledFrame, StyledLabel, Colors, ModernButton

# Import configuration
try:
    from utils.config import Config
    from utils.logger import setup_logger
except ImportError:
    # Fallback configuration
    class Config:
        DEFAULT_USERNAME = "admin"
        DEFAULT_PASSWORD = "password123"
        WINDOW_SIZE = (1200, 800)
    
    def setup_logger():
        import logging
        return logging.getLogger(__name__)


class LoginWindow(QMainWindow):
    """
    PyQt5 Enhanced login window with secure authentication.
    Migrated from Tkinter while preserving all functionality.
    """
    
    # Signal emitted when authentication is successful
    authentication_successful = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        
        # Initialize configuration
        self.config = Config()
        
        # Initialize logger
        try:
            self.logger = setup_logger()
            self.logger.info("Starting PyQt5 secure login interface...")
        except:
            print("Starting PyQt5 login interface...")
        
        # Initialize state
        self.authenticated = False
        
        # Setup window
        self.setup_window()
        
        # Create interface
        self.create_interface()
        
        print("✅ PyQt5 Login window initialized successfully")
    
    def setup_window(self):
        """Setup main window properties with responsive design"""
        self.setWindowTitle("🛡️ AI Video Detection - Login")
        self.setFixedSize(800, 600)

        # Set minimum size for responsive behavior
        self.setMinimumSize(600, 500)

        # Set window background color with modern gradient
        self.setStyleSheet(f"""
            QMainWindow {{
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                          stop: 0 {Colors.BACKGROUND},
                                          stop: 1 {Colors.GRAY_50});
            }}
        """)

        # Center window on screen
        center_window(self, 800, 600)

        # Set window flags to prevent resizing
        self.setWindowFlags(Qt.Window | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
    
    def create_interface(self):
        """Create the login interface"""
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Create sections
        self.create_header(main_layout)
        self.create_login_section(main_layout)
        self.create_footer(main_layout)
    
    def create_header(self, parent_layout):
        """Create header section"""
        # Header frame
        header_frame = StyledFrame(bg_color=Colors.DARK)
        header_frame.setFixedHeight(120)
        parent_layout.addWidget(header_frame)
        
        # Header layout - 8px grid system
        header_layout = QVBoxLayout(header_frame)
        header_layout.setAlignment(Qt.AlignCenter)
        header_layout.setContentsMargins(24, 24, 24, 24)  # 24px margins for header
        
        # Logo
        logo_label = QLabel("🛡️")
        logo_label.setFont(create_font('Arial', 48))
        logo_label.setStyleSheet(f"color: {Colors.INFO}; background: transparent;")
        logo_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(logo_label)
        
        # Title
        title_label = QLabel("AI Video Detection")
        title_label.setFont(create_font('Arial', 20, bold=True))
        title_label.setStyleSheet(f"color: white; background: transparent;")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label)
        
        # Subtitle
        subtitle_label = QLabel("Security Monitoring & Analytics")
        subtitle_label.setFont(create_font('Arial', 12))
        subtitle_label.setStyleSheet(f"color: {Colors.GRAY_LIGHT}; background: transparent;")
        subtitle_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(subtitle_label)
    
    def create_login_section(self, parent_layout):
        """Create login form section"""
        # Login container
        login_container = QWidget()
        login_container.setStyleSheet(f"background-color: {Colors.BACKGROUND};")
        parent_layout.addWidget(login_container, 1)  # Expand to fill space
        
        # Container layout - 8px grid system
        container_layout = QVBoxLayout(login_container)
        container_layout.setAlignment(Qt.AlignCenter)
        container_layout.setContentsMargins(48, 48, 48, 48)  # 48px margins for container
        
        # Login card
        login_card = StyledFrame(bg_color=Colors.WHITE, border_color='#E0E0E0', border_width=2)
        login_card.setFixedSize(400, 450)
        container_layout.addWidget(login_card)
        
        # Card layout - 8px grid system
        card_layout = QVBoxLayout(login_card)
        card_layout.setContentsMargins(48, 40, 48, 40)  # 48px horizontal, 40px vertical
        card_layout.setSpacing(24)  # 24px spacing between form elements
        
        # Card title
        card_title = QLabel("🔐")
        card_title.setFont(create_font('Arial', 18, bold=True))
        card_title.setStyleSheet(f"color: {Colors.DARK}; background: transparent;")
        card_title.setAlignment(Qt.AlignCenter)
        card_layout.addWidget(card_title)
        
        # Username field
        self.create_username_field(card_layout)
        
        # Password field
        self.create_password_field(card_layout)
        
        # Login button
        self.create_login_button(card_layout)
        
        # Features preview
        self.create_features_preview(card_layout)
    
    def create_username_field(self, parent_layout):
        """Create username input field"""
        # Username label
        username_label = QLabel("👤 Username:")
        username_label.setFont(create_font('Arial', 12, bold=True))
        username_label.setStyleSheet(f"color: {Colors.DARK}; background: transparent;")
        parent_layout.addWidget(username_label)
        
        # Username entry
        self.username_entry = QLineEdit()
        self.username_entry.setFont(create_font('Arial', 14))
        self.username_entry.setStyleSheet(f"""
            QLineEdit {{
                background-color: #F8F9FA;
                border: 1px solid #E0E0E0;
                border-radius: 4px;
                padding: 8px 12px;
                font-size: 14px;
            }}
            QLineEdit:focus {{
                border: 2px solid {Colors.PRIMARY};
            }}
        """)
        self.username_entry.setText("admin")  # Default username
        self.username_entry.setPlaceholderText("Enter username")
        parent_layout.addWidget(self.username_entry)
    
    def create_password_field(self, parent_layout):
        """Create password input field"""
        # Password label
        password_label = QLabel("🔒 Password:")
        password_label.setFont(create_font('Arial', 12, bold=True))
        password_label.setStyleSheet(f"color: {Colors.DARK}; background: transparent;")
        parent_layout.addWidget(password_label)
        
        # Password entry
        self.password_entry = QLineEdit()
        self.password_entry.setFont(create_font('Arial', 14))
        self.password_entry.setEchoMode(QLineEdit.Password)
        self.password_entry.setStyleSheet(f"""
            QLineEdit {{
                background-color: #F8F9FA;
                border: 1px solid #E0E0E0;
                border-radius: 4px;
                padding: 8px 12px;
                font-size: 14px;
            }}
            QLineEdit:focus {{
                border: 2px solid {Colors.PRIMARY};
            }}
        """)
        self.password_entry.setText("password123")  # Default password for demo
        self.password_entry.setPlaceholderText("Enter password")
        parent_layout.addWidget(self.password_entry)
        
        # Show/Hide password checkbox
        self.show_password_check = QCheckBox("Show password")
        self.show_password_check.setFont(create_font('Arial', 10))
        self.show_password_check.setStyleSheet(f"""
            QCheckBox {{
                color: {Colors.GRAY_DARK};
                background: transparent;
            }}
            QCheckBox::indicator {{
                width: 16px;
                height: 16px;
            }}
        """)
        self.show_password_check.stateChanged.connect(self.toggle_password_visibility)
        parent_layout.addWidget(self.show_password_check)
    
    def create_login_button(self, parent_layout):
        """Create login button with modern styling"""
        self.login_button = ModernButton("🔐 Login", style='primary', size='large')
        self.login_button.setFixedHeight(45)
        self.login_button.clicked.connect(self.authenticate)
        parent_layout.addWidget(self.login_button)

        # Connect Enter key to login
        self.username_entry.returnPressed.connect(self.authenticate)
        self.password_entry.returnPressed.connect(self.authenticate)
    
    def create_features_preview(self, parent_layout):
        """Create features preview section"""
        # Features label
        features_label = QLabel("Features:")
        features_label.setFont(create_font('Arial', 12, bold=True))
        features_label.setStyleSheet(f"color: {Colors.DARK}; background: transparent;")
        parent_layout.addWidget(features_label)
        
        # Features text
        features_text = """• Real-time facial expression detection
• Advanced age estimation and analysis
• Intelligent object detection and tracking
• Security anomaly monitoring and alerts
• Comprehensive analytics and reporting"""
        
        features_display = QLabel(features_text)
        features_display.setFont(create_font('Arial', 10))
        features_display.setStyleSheet(f"color: #34495E; background: transparent;")
        features_display.setWordWrap(True)
        parent_layout.addWidget(features_display)
    
    def create_footer(self, parent_layout):
        """Create footer section"""
        footer_frame = StyledFrame(bg_color='#34495E')
        footer_frame.setFixedHeight(60)
        parent_layout.addWidget(footer_frame)
        
        # Footer layout - 8px grid system
        footer_layout = QVBoxLayout(footer_frame)
        footer_layout.setAlignment(Qt.AlignCenter)
        footer_layout.setContentsMargins(24, 16, 24, 16)  # 24px horizontal, 16px vertical
        
        # Footer text
        footer_text = QLabel("AI Video Detection")
        footer_text.setFont(create_font('Arial', 10))
        footer_text.setStyleSheet(f"color: {Colors.GRAY_LIGHT}; background: transparent;")
        footer_text.setAlignment(Qt.AlignCenter)
        footer_layout.addWidget(footer_text)
        
        # Status text
        status_text = QLabel("🟢 Online | Ready for Authentication")
        status_text.setFont(create_font('Arial', 9))
        status_text.setStyleSheet(f"color: {Colors.SUCCESS}; background: transparent;")
        status_text.setAlignment(Qt.AlignCenter)
        footer_layout.addWidget(status_text)
    
    def toggle_password_visibility(self, state):
        """Toggle password field visibility"""
        if state == Qt.Checked:
            self.password_entry.setEchoMode(QLineEdit.Normal)
        else:
            self.password_entry.setEchoMode(QLineEdit.Password)

    def authenticate(self):
        """Handle authentication"""
        try:
            username = self.username_entry.text().strip()
            password = self.password_entry.text().strip()

            print(f"🔐 Authentication attempt - Username: {username}")

            # Check credentials (case-insensitive username)
            if (username.lower() == self.config.DEFAULT_USERNAME.lower() and
                password == self.config.DEFAULT_PASSWORD):

                print("✅ Authentication successful!")
                self.authenticated = True

                # Show success message
                show_info_message("Login Successful",
                                f"🎉 Welcome to AI Video Detection Tool!\n\n"
                                f"User: {username}\n"
                                f"Access Level: Administrator\n"
                                f"Tool Status: All modules online",
                                self)

                # Emit authentication successful signal
                self.authentication_successful.emit()

                # Close login window and start main application
                self.close()
                self.start_main_application()

            else:
                print("❌ Authentication failed!")
                show_error_message("Login Failed",
                                 "❌ Invalid credentials!\n\n"
                                 "Please check your username and password.\n"
                                 "Default: admin / password123",
                                 self)

                # Clear password field and focus
                self.password_entry.clear()
                self.password_entry.setFocus()

        except Exception as e:
            print(f"❌ Authentication error: {e}")
            show_error_message("Authentication Error",
                             f"An error occurred during authentication:\n{str(e)}",
                             self)

    def start_main_application(self):
        """Start the main application after successful login"""
        try:
            print("🚀 Starting PyQt5 main application...")

            # Try to import and start the main window
            try:
                from .main_window import MainWindow
                print("✅ PyQt5 Main window module imported successfully")

                # Create and show main window
                self.main_app = MainWindow()
                self.main_app.show()

            except ImportError as e:
                print(f"⚠️ PyQt5 Main window import error: {e}")
                print("🔄 Starting fallback interface...")
                self.start_fallback_interface()

        except Exception as e:
            print(f"❌ Error starting main application: {e}")
            show_error_message("Application Error",
                             f"Error starting main application:\n{str(e)}",
                             self)

    def start_fallback_interface(self):
        """Start fallback interface when main window fails"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QPushButton

            # Create simple fallback dialog
            fallback_dialog = QDialog(self)
            fallback_dialog.setWindowTitle("🛡️ AI Video Detection - Fallback")
            fallback_dialog.setFixedSize(600, 400)

            layout = QVBoxLayout(fallback_dialog)

            # Title
            title = QLabel("🎯 Tool Dashboard - Fallback Mode")
            title.setFont(create_font('Arial', 16, bold=True))
            title.setAlignment(Qt.AlignCenter)
            layout.addWidget(title)

            # Status
            status_text = """
🟢 System Status: Online and Ready
🟢 Authentication: Successful
🟢 Core Modules: Loaded
⚠️ Advanced Features: Limited Mode

📋 Available Functions:
• Camera access and testing
• Basic video processing
• System configuration
• Help and documentation
            """

            status_label = QLabel(status_text)
            status_label.setFont(create_font('Arial', 12))
            status_label.setAlignment(Qt.AlignLeft)
            layout.addWidget(status_label)

            # Test camera button - using ModernButton for consistency
            camera_btn = ModernButton("📷 Test Camera", style='primary', size='medium')
            camera_btn.clicked.connect(self.test_camera)
            layout.addWidget(camera_btn)

            # Close button - using ModernButton for consistency
            close_btn = ModernButton("🚪 Close", style='secondary', size='medium')
            close_btn.clicked.connect(fallback_dialog.close)
            layout.addWidget(close_btn)

            fallback_dialog.exec_()

        except Exception as e:
            print(f"❌ Fallback interface error: {e}")
            show_error_message("Critical Error",
                             "Unable to start any interface. Please check your PyQt5 installation.",
                             self)

    def test_camera(self):
        """Test camera functionality"""
        try:
            import cv2
            cap = cv2.VideoCapture(0)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret:
                    show_info_message("Camera Test",
                                    f"✅ Camera is working perfectly!\n\n"
                                    f"Resolution: {frame.shape[1]}x{frame.shape[0]}\n"
                                    f"Status: Ready for detection",
                                    self)
                else:
                    show_error_message("Camera Test",
                                     "⚠️ Camera detected but cannot read frames",
                                     self)
                cap.release()
            else:
                show_error_message("Camera Test",
                                 "❌ No camera detected\n\nPlease check camera connection",
                                 self)
        except ImportError:
            show_error_message("Camera Test",
                             "❌ OpenCV not available\n\nInstall with: pip install opencv-python",
                             self)
        except Exception as e:
            show_error_message("Camera Test",
                             f"❌ Camera error:\n{str(e)}",
                             self)

    def closeEvent(self, event):
        """Handle window close event"""
        if not self.authenticated:
            print("🛑 Login window closed without authentication")
        event.accept()

    def run(self):
        """Start the login window (for compatibility with Tkinter version)"""
        print("🚀 Starting PyQt5 login window...")

        # Ensure application instance exists first
        app = ensure_application()
        if not app:
            app = QApplication(sys.argv)

        # Now show the window
        self.show()

        # Focus on username field
        self.username_entry.setFocus()

        return app.exec_()


# Standalone execution for testing
if __name__ == "__main__":
    app = ensure_application()
    if not app:
        app = QApplication(sys.argv)

    login_window = LoginWindow()
    login_window.show()

    sys.exit(app.exec_())
