"""
PyQt5 Dashboard Window

This module contains the PyQt5 implementation of the dashboard window,
providing analytics and reporting functionality for the AI Video Detection system.
"""

import sys
from datetime import datetime
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QFrame, QScrollArea, QTabWidget, QTextEdit,
    QGridLayout, QSizePolicy
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

# Import utilities and base components
from .utils import (
    center_window, show_info_message, show_error_message,
    ensure_application, create_font
)
from .base_components import StyledFrame, StyledLabel, Colors, ModernButton


class DashboardWindow(QMainWindow):
    """
    PyQt5 Dashboard Window for AI Video Detection analytics.
    Provides real-time statistics, reports, and data visualization.
    """
    
    def __init__(self):
        super().__init__()
        
        # Initialize data
        self.current_data = {}
        self.stat_labels = {}
        self.auto_refresh = True
        self.refresh_interval = 5000  # 5 seconds
        
        # Setup window
        self.setup_window()
        
        # Create interface
        self.create_interface()
        
        # Setup auto-refresh timer
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        if self.auto_refresh:
            self.refresh_timer.start(self.refresh_interval)
        
        print("✅ PyQt5 Dashboard window initialized")
    
    def setup_window(self):
        """Setup dashboard window properties"""
        self.setWindowTitle("🎯 AI Detection Dashboard - Analytics")
        self.setGeometry(100, 100, 1200, 800)
        
        # Set window background color
        self.setStyleSheet(f"QMainWindow {{ background-color: #F8F9FA; }}")
        
        # Center window on screen
        center_window(self, 1200, 800)
    
    def create_interface(self):
        """Create the dashboard interface"""
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Create sections
        self.create_header(main_layout)
        self.create_controls(main_layout)
        self.create_content_tabs(main_layout)
    
    def create_header(self, parent_layout):
        """Create dashboard header"""
        header_frame = StyledFrame(bg_color=Colors.DARK)
        header_frame.setFixedHeight(80)
        parent_layout.addWidget(header_frame)
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(24, 24, 24, 24)  # 24px margins for header
        
        # Title
        title_label = QLabel("🎯 AI Detection Dashboard")
        title_label.setFont(create_font('Arial', 20, bold=True))
        title_label.setStyleSheet("color: white; background: transparent;")
        header_layout.addWidget(title_label)
        
        # Status and time
        status_frame = QWidget()
        status_layout = QVBoxLayout(status_frame)
        status_layout.setContentsMargins(0, 0, 0, 0)
        
        self.status_label = QLabel("🟢 ACTIVE")
        self.status_label.setFont(create_font('Arial', 12, bold=True))
        self.status_label.setStyleSheet(f"color: {Colors.SUCCESS}; background: transparent;")
        self.status_label.setAlignment(Qt.AlignRight)
        status_layout.addWidget(self.status_label)
        
        self.time_label = QLabel("")
        self.time_label.setFont(create_font('Arial', 10))
        self.time_label.setStyleSheet(f"color: {Colors.GRAY_LIGHT}; background: transparent;")
        self.time_label.setAlignment(Qt.AlignRight)
        status_layout.addWidget(self.time_label)
        
        header_layout.addWidget(status_frame)
        
        # Update time
        self.update_time()
        
        # Timer for time updates
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)
    
    def create_controls(self, parent_layout):
        """Create dashboard controls"""
        controls_frame = StyledFrame(bg_color=Colors.WHITE, border_color='#E0E0E0', border_width=1)
        controls_frame.setFixedHeight(80)
        parent_layout.addWidget(controls_frame)
        
        controls_layout = QHBoxLayout(controls_frame)
        controls_layout.setContentsMargins(24, 16, 24, 16)  # 24px horizontal, 16px vertical
        
        # Refresh controls - using ModernButton for consistency
        refresh_btn = ModernButton("🔄 Refresh Now", style='success', size='medium')
        refresh_btn.clicked.connect(self.refresh_data)
        controls_layout.addWidget(refresh_btn)

        # Auto-refresh toggle - using ModernButton for consistency
        self.auto_refresh_btn = ModernButton("🔄 Auto-Refresh: ON", style='primary', size='medium')
        self.auto_refresh_btn.clicked.connect(self.toggle_auto_refresh)
        controls_layout.addWidget(self.auto_refresh_btn)

        # Report generation - using ModernButton for consistency
        pdf_btn = ModernButton("📄 Generate PDF", style='success', size='medium')
        pdf_btn.clicked.connect(self.generate_pdf_report)
        controls_layout.addWidget(pdf_btn)

        csv_btn = ModernButton("📊 Export CSV", style='warning', size='medium')
        csv_btn.clicked.connect(self.export_csv_data)
        controls_layout.addWidget(csv_btn)

        # Close button - using ModernButton for consistency
        close_btn = ModernButton("❌ Close", style='danger', size='medium')
        close_btn.clicked.connect(self.close)
        controls_layout.addWidget(close_btn)
        
        controls_layout.addStretch()
    
    def create_content_tabs(self, parent_layout):
        """Create tabbed content area"""
        # Create tab widget
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #E0E0E0;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #F8F9FA;
                border: 1px solid #E0E0E0;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 1px solid white;
            }
            QTabBar::tab:hover {
                background-color: #E8F4FD;
            }
        """)
        parent_layout.addWidget(self.tab_widget)
        
        # Create tabs
        self.create_overview_tab()
        self.create_statistics_tab()
        self.create_reports_tab()
    
    def create_overview_tab(self):
        """Create overview tab"""
        overview_widget = QWidget()
        overview_layout = QVBoxLayout(overview_widget)
        overview_layout.setContentsMargins(24, 24, 24, 24)  # 24px margins for tab content
        
        # Statistics cards
        self.create_statistics_cards(overview_layout)
        
        # Recent activity
        self.create_recent_activity(overview_layout)
        
        self.tab_widget.addTab(overview_widget, "📊 Overview")
    
    def create_statistics_tab(self):
        """Create detailed statistics tab"""
        stats_widget = QWidget()
        stats_layout = QVBoxLayout(stats_widget)
        stats_layout.setContentsMargins(24, 24, 24, 24)  # 24px margins for tab content
        
        # Detailed statistics
        stats_title = QLabel("📈 Detailed Statistics")
        stats_title.setFont(create_font('Arial', 16, bold=True))
        stats_title.setStyleSheet(f"color: {Colors.DARK}; margin-bottom: 20px;")
        stats_layout.addWidget(stats_title)
        
        # Statistics content
        stats_content = QLabel("""
📊 Detection Summary:
• Total Sessions: 0
• Frames Processed: 0
• Faces Detected: 0
• Objects Detected: 0
• Anomalies Detected: 0

🎯 Performance Metrics:
• Average FPS: 0.0
• Detection Accuracy: N/A
• System Uptime: 0 minutes

📈 Trends:
• Most Active Hour: N/A
• Common Objects: N/A
• Alert Frequency: N/A
        """)
        stats_content.setFont(create_font('Arial', 12))
        stats_content.setStyleSheet(f"color: {Colors.DARK}; background: white; padding: 20px; border: 1px solid #E0E0E0;")
        stats_layout.addWidget(stats_content)
        
        stats_layout.addStretch()
        
        self.tab_widget.addTab(stats_widget, "📈 Statistics")
    
    def create_reports_tab(self):
        """Create reports tab"""
        reports_widget = QWidget()
        reports_layout = QVBoxLayout(reports_widget)
        reports_layout.setContentsMargins(24, 24, 24, 24)  # 24px margins for tab content
        
        # Reports title
        reports_title = QLabel("📄 Reports & Export")
        reports_title.setFont(create_font('Arial', 16, bold=True))
        reports_title.setStyleSheet(f"color: {Colors.DARK}; margin-bottom: 20px;")
        reports_layout.addWidget(reports_title)
        
        # Report options
        report_options = QLabel("""
Available Report Formats:

📄 PDF Report: Comprehensive analysis document with charts and summaries
📊 CSV Export: Raw data for external analysis and processing
🔗 JSON Export: API-ready structured data format
📈 Summary Report: Quick overview with key metrics

Report Features:
• Real-time data integration
• Customizable time ranges
• Automated generation
• Professional formatting
        """)
        report_options.setFont(create_font('Arial', 12))
        report_options.setStyleSheet(f"color: {Colors.DARK}; background: white; padding: 20px; border: 1px solid #E0E0E0;")
        reports_layout.addWidget(report_options)
        
        reports_layout.addStretch()
        
        self.tab_widget.addTab(reports_widget, "📄 Reports")
    
    def create_statistics_cards(self, parent_layout):
        """Create statistics cards"""
        cards_frame = QFrame()
        cards_layout = QGridLayout(cards_frame)
        cards_layout.setSpacing(16)  # 16px spacing between cards
        
        # Statistics data
        stats_data = [
            ("📹", "Frames Processed", "0", Colors.PRIMARY),
            ("👤", "Faces Detected", "0", Colors.SUCCESS),
            ("🔍", "Objects Detected", "0", Colors.WARNING),
            ("🚨", "Anomalies Detected", "0", Colors.DANGER),
        ]
        
        for i, (icon, title, value, color) in enumerate(stats_data):
            card = self.create_stat_card(icon, title, value, color)
            row = i // 2
            col = i % 2
            cards_layout.addWidget(card, row, col)
            
            # Store reference for updates
            self.stat_labels[title.lower().replace(' ', '_')] = card.findChild(QLabel, "value_label")
        
        parent_layout.addWidget(cards_frame)
    
    def create_stat_card(self, icon, title, value, color):
        """Create a single statistics card"""
        card = StyledFrame(bg_color=Colors.WHITE, border_color='#E0E0E0', border_width=1)
        card.setFixedHeight(120)
        
        card_layout = QVBoxLayout(card)
        card_layout.setContentsMargins(24, 16, 24, 16)  # 24px horizontal, 16px vertical
        card_layout.setAlignment(Qt.AlignCenter)
        
        # Icon
        icon_label = QLabel(icon)
        icon_label.setFont(create_font('Arial', 24))
        icon_label.setStyleSheet(f"color: {color}; background: transparent;")
        icon_label.setAlignment(Qt.AlignCenter)
        card_layout.addWidget(icon_label)
        
        # Value
        value_label = QLabel(value)
        value_label.setObjectName("value_label")  # For finding later
        value_label.setFont(create_font('Arial', 18, bold=True))
        value_label.setStyleSheet(f"color: {Colors.DARK}; background: transparent;")
        value_label.setAlignment(Qt.AlignCenter)
        card_layout.addWidget(value_label)
        
        # Title
        title_label = QLabel(title)
        title_label.setFont(create_font('Arial', 10))
        title_label.setStyleSheet(f"color: {Colors.GRAY_DARK}; background: transparent;")
        title_label.setAlignment(Qt.AlignCenter)
        card_layout.addWidget(title_label)
        
        return card
    
    def create_recent_activity(self, parent_layout):
        """Create recent activity section"""
        activity_frame = StyledFrame(bg_color=Colors.WHITE, border_color='#E0E0E0', border_width=1)
        parent_layout.addWidget(activity_frame)
        
        activity_layout = QVBoxLayout(activity_frame)
        activity_layout.setContentsMargins(24, 16, 24, 16)  # 24px horizontal, 16px vertical
        
        # Activity title
        activity_title = QLabel("📋 Recent Activity")
        activity_title.setFont(create_font('Arial', 14, bold=True))
        activity_title.setStyleSheet(f"color: {Colors.DARK}; background: transparent;")
        activity_layout.addWidget(activity_title)
        
        # Activity log
        self.activity_log = QTextEdit()
        self.activity_log.setMaximumHeight(200)
        self.activity_log.setStyleSheet("""
            QTextEdit {
                background-color: #F8F9FA;
                border: 1px solid #E0E0E0;
                border-radius: 4px;
                padding: 10px;
                font-family: Arial;
                font-size: 10px;
            }
        """)
        self.activity_log.setReadOnly(True)
        self.activity_log.append(f"[{datetime.now().strftime('%H:%M:%S')}] Dashboard initialized")
        self.activity_log.append(f"[{datetime.now().strftime('%H:%M:%S')}] Ready for real-time monitoring")
        activity_layout.addWidget(self.activity_log)

    def update_time(self):
        """Update time display"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)

    def refresh_data(self):
        """Refresh dashboard data"""
        try:
            print("🔄 Refreshing dashboard data...")

            # Simulate data refresh (in real implementation, this would fetch from database)
            import random

            # Update statistics with sample data
            sample_data = {
                'frames_processed': random.randint(1000, 5000),
                'faces_detected': random.randint(50, 200),
                'objects_detected': random.randint(100, 500),
                'anomalies_detected': random.randint(0, 10),
            }

            # Update stat cards
            for key, value in sample_data.items():
                if key in self.stat_labels and self.stat_labels[key]:
                    self.stat_labels[key].setText(str(value))

            # Add activity log entry
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.activity_log.append(f"[{timestamp}] Data refreshed - {sum(sample_data.values())} total detections")

            # Scroll to bottom
            scrollbar = self.activity_log.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())

            print("✅ Dashboard data refreshed")

        except Exception as e:
            print(f"❌ Error refreshing dashboard data: {e}")

    def toggle_auto_refresh(self):
        """Toggle auto-refresh functionality"""
        try:
            self.auto_refresh = not self.auto_refresh

            if self.auto_refresh:
                self.auto_refresh_btn.setText("🔄 Auto-Refresh: ON")
                self.auto_refresh_btn.style_type = 'primary'
                self.auto_refresh_btn.update_modern_style()
                self.refresh_timer.start(self.refresh_interval)
                print("✅ Auto-refresh enabled")
            else:
                self.auto_refresh_btn.setText("🔄 Auto-Refresh: OFF")
                self.auto_refresh_btn.style_type = 'secondary'
                self.auto_refresh_btn.update_modern_style()
                self.refresh_timer.stop()
                print("✅ Auto-refresh disabled")

        except Exception as e:
            print(f"❌ Error toggling auto-refresh: {e}")

    def generate_pdf_report(self):
        """Generate PDF report"""
        try:
            print("📄 Generating PDF report...")
            show_info_message("PDF Report",
                            "📄 PDF Report Generation\n\n"
                            "This feature will generate a comprehensive PDF report including:\n\n"
                            "• Detection statistics and trends\n"
                            "• Performance metrics and charts\n"
                            "• Activity timeline and summaries\n"
                            "• System health and alerts\n\n"
                            "Report will be saved to the reports/ directory.",
                            self)
        except Exception as e:
            print(f"❌ Error generating PDF report: {e}")
            show_error_message("PDF Error", f"Error generating PDF report:\n{str(e)}", self)

    def export_csv_data(self):
        """Export data to CSV"""
        try:
            print("📊 Exporting CSV data...")
            show_info_message("CSV Export",
                            "📊 CSV Data Export\n\n"
                            "This feature will export detection data to CSV format including:\n\n"
                            "• Timestamp and session information\n"
                            "• Detection results and confidence scores\n"
                            "• Performance metrics and statistics\n"
                            "• Raw data for external analysis\n\n"
                            "CSV file will be saved to the exports/ directory.",
                            self)
        except Exception as e:
            print(f"❌ Error exporting CSV data: {e}")
            show_error_message("CSV Error", f"Error exporting CSV data:\n{str(e)}", self)

    def show_dashboard(self):
        """Show the dashboard window"""
        print("🎯 Opening AI Detection Dashboard...")
        self.show()
        self.raise_()
        self.activateWindow()

    def closeEvent(self, event):
        """Handle window close event"""
        print("🛑 Closing dashboard window...")
        if hasattr(self, 'refresh_timer'):
            self.refresh_timer.stop()
        if hasattr(self, 'time_timer'):
            self.time_timer.stop()
        event.accept()


# Standalone execution for testing
if __name__ == "__main__":
    app = ensure_application()
    if not app:
        app = QApplication(sys.argv)

    dashboard = DashboardWindow()
    dashboard.show()

    sys.exit(app.exec_())
