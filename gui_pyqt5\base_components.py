"""
PyQt5 Base Components

This module contains PyQt5 equivalents of custom Tkinter components
used throughout the AI Video Detection application.
"""

import sys
from PyQt5.QtWidgets import (
    QWidget, QPushButton, QLabel, QFrame, QVBoxLayout, QHB<PERSON><PERSON>ayout,
    QGridLayout, QScrollArea, QApplication, QSizePolicy
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QRect
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor, QFont, QPalette, QLinearGradient


# Modern color scheme for professional appearance
class Colors:
    # Primary colors - Modern blue palette
    PRIMARY = '#2563EB'      # Modern blue
    PRIMARY_LIGHT = '#3B82F6'
    PRIMARY_DARK = '#1D4ED8'

    # Status colors
    SUCCESS = '#10B981'      # Modern green
    SUCCESS_LIGHT = '#34D399'
    SUCCESS_DARK = '#059669'

    WARNING = '#F59E0B'      # Modern amber
    WARNING_LIGHT = '#FBBF24'
    WARNING_DARK = '#D97706'

    DANGER = '#EF4444'       # Modern red
    DANGER_LIGHT = '#F87171'
    DANGER_DARK = '#DC2626'

    INFO = '#06B6D4'         # Modern cyan
    INFO_LIGHT = '#22D3EE'
    INFO_DARK = '#0891B2'

    # Neutral colors - Modern gray palette
    WHITE = '#FFFFFF'
    GRAY_50 = '#F9FAFB'
    GRAY_100 = '#F3F4F6'
    GRAY_200 = '#E5E7EB'
    GRAY_300 = '#D1D5DB'
    GRAY_400 = '#9CA3AF'
    GRAY_500 = '#6B7280'
    GRAY_600 = '#4B5563'
    GRAY_700 = '#374151'
    GRAY_800 = '#1F2937'
    GRAY_900 = '#111827'
    BLACK = '#000000'

    # Background colors
    BACKGROUND = '#F8FAFC'   # Light blue-gray
    BACKGROUND_DARK = '#0F172A'  # Dark slate

    # Legacy aliases for compatibility
    LIGHT = GRAY_100
    DARK = GRAY_800
    SECONDARY = GRAY_500
    GRAY_LIGHT = GRAY_300
    GRAY_DARK = GRAY_600

    # Gradient colors for modern effects
    GRADIENT_START = '#667EEA'
    GRADIENT_END = '#764BA2'

    # Shadow colors
    SHADOW_LIGHT = 'rgba(0, 0, 0, 0.1)'
    SHADOW_MEDIUM = 'rgba(0, 0, 0, 0.15)'
    SHADOW_DARK = 'rgba(0, 0, 0, 0.25)'


class RoundedButton(QPushButton):
    """
    PyQt5 equivalent of the custom Tkinter RoundedButton class.
    Creates a button with rounded corners and hover effects.
    """
    
    def __init__(self, text="", parent=None, bg_color='#3498DB', fg_color='white', 
                 width=120, height=40, corner_radius=10, font_family='Arial', 
                 font_size=10, font_weight='bold'):
        super().__init__(text, parent)
        
        # Store properties
        self.bg_color = QColor(bg_color)
        self.fg_color = QColor(fg_color)
        self.corner_radius = corner_radius
        self.enabled_state = True
        self.disabled_bg = QColor('#BDC3C7')
        self.disabled_fg = QColor('#7F8C8D')
        self.hover_bg = self.lighten_color(self.bg_color)
        
        # Set size
        self.setFixedSize(width, height)
        
        # Set font
        font = QFont(font_family, font_size)
        if font_weight == 'bold':
            font.setBold(True)
        self.setFont(font)
        
        # Set cursor
        self.setCursor(Qt.PointingHandCursor)
        
        # Apply initial styling
        self.update_style()
        
        # Enable hover events
        self.setAttribute(Qt.WA_Hover, True)
    
    def lighten_color(self, color, factor=30):
        """Lighten a QColor by a given factor"""
        r = min(255, color.red() + factor)
        g = min(255, color.green() + factor)
        b = min(255, color.blue() + factor)
        return QColor(r, g, b)
    
    def update_style(self):
        """Update button styling based on current state with modern design"""
        if self.enabled_state:
            bg = self.bg_color
            fg = self.fg_color
        else:
            bg = self.disabled_bg
            fg = self.disabled_fg

        style = f"""
            QPushButton {{
                background-color: {bg.name()};
                color: {fg.name()};
                border: none;
                border-radius: {self.corner_radius}px;
                padding: 10px 20px;
                font-weight: 600;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                transition: all 0.2s ease;
            }}
            QPushButton:hover {{
                background-color: {self.hover_bg.name()};
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            }}
            QPushButton:pressed {{
                background-color: {self.lighten_color(self.bg_color, -20).name()};
                transform: translateY(0px);
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }}
            QPushButton:disabled {{
                background-color: {self.disabled_bg.name()};
                color: {self.disabled_fg.name()};
                box-shadow: none;
            }}
        """
        self.setStyleSheet(style)
    
    def setEnabled(self, enabled):
        """Override setEnabled to update styling"""
        super().setEnabled(enabled)
        self.enabled_state = enabled
        self.update_style()
    
    def config(self, **kwargs):
        """Configuration method similar to Tkinter"""
        if 'text' in kwargs:
            self.setText(kwargs['text'])
        if 'bg' in kwargs:
            self.bg_color = QColor(kwargs['bg'])
            self.hover_bg = self.lighten_color(self.bg_color)
            self.update_style()
        if 'state' in kwargs:
            enabled = kwargs['state'] != 'disabled'
            self.setEnabled(enabled)


class ColoredButton(QPushButton):
    """
    PyQt5 equivalent of the ColoredButton class from dashboard.
    Provides styled buttons with hover effects and color management.
    """
    
    def __init__(self, text="", parent=None, bg_color='#3498DB', fg_color='white',
                 width=15, height=1, font_family='Arial', font_size=10, font_weight='bold'):
        super().__init__(text, parent)
        
        # Store colors
        self.normal_bg = QColor(bg_color)
        self.fg_color = QColor(fg_color)
        self.hover_bg = self.lighten_color(self.normal_bg)
        
        # Set font
        font = QFont(font_family, font_size)
        if font_weight == 'bold':
            font.setBold(True)
        self.setFont(font)
        
        # Set size (convert from character-based to pixels)
        char_width = 8  # Approximate character width
        char_height = 20  # Approximate character height
        self.setFixedSize(width * char_width, height * char_height + 16)
        
        # Set cursor
        self.setCursor(Qt.PointingHandCursor)
        
        # Apply styling
        self.update_style()
        
        # Enable hover events
        self.setAttribute(Qt.WA_Hover, True)
    
    def lighten_color(self, color, factor=30):
        """Lighten a QColor by a given factor"""
        r = min(255, color.red() + factor)
        g = min(255, color.green() + factor)
        b = min(255, color.blue() + factor)
        return QColor(r, g, b)
    
    def update_style(self):
        """Update button styling"""
        style = f"""
            QPushButton {{
                background-color: {self.normal_bg.name()};
                color: {self.fg_color.name()};
                border: none;
                border-radius: 4px;
                padding: 8px 12px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self.hover_bg.name()};
            }}
            QPushButton:pressed {{
                background-color: {self.lighten_color(self.normal_bg, -20).name()};
            }}
        """
        self.setStyleSheet(style)


class ModernButton(QPushButton):
    """
    Modern button with gradient effects and enhanced styling
    """

    def __init__(self, text="", parent=None, style='primary', size='medium'):
        super().__init__(text, parent)

        self.style_type = style
        self.size_type = size

        # Set size based on type
        if size == 'small':
            self.setFixedHeight(32)
            font_size = 12
            padding = '6px 12px'
        elif size == 'large':
            self.setFixedHeight(48)
            font_size = 16
            padding = '12px 24px'
        else:  # medium
            self.setFixedHeight(40)
            font_size = 14
            padding = '8px 16px'

        # Set font
        font = QFont('Segoe UI', font_size, QFont.Medium)
        self.setFont(font)

        # Set cursor
        self.setCursor(Qt.PointingHandCursor)

        # Apply styling
        self.update_modern_style()

        # Enable hover events
        self.setAttribute(Qt.WA_Hover, True)

    def update_modern_style(self):
        """Apply modern styling based on style type"""
        styles = {
            'primary': {
                'bg': Colors.PRIMARY,
                'hover': Colors.PRIMARY_LIGHT,
                'active': Colors.PRIMARY_DARK,
                'text': Colors.WHITE
            },
            'success': {
                'bg': Colors.SUCCESS,
                'hover': Colors.SUCCESS_LIGHT,
                'active': Colors.SUCCESS_DARK,
                'text': Colors.WHITE
            },
            'warning': {
                'bg': Colors.WARNING,
                'hover': Colors.WARNING_LIGHT,
                'active': Colors.WARNING_DARK,
                'text': Colors.WHITE
            },
            'danger': {
                'bg': Colors.DANGER,
                'hover': Colors.DANGER_LIGHT,
                'active': Colors.DANGER_DARK,
                'text': Colors.WHITE
            },
            'secondary': {
                'bg': Colors.GRAY_500,
                'hover': Colors.GRAY_400,
                'active': Colors.GRAY_600,
                'text': Colors.WHITE
            },
            'outline': {
                'bg': 'transparent',
                'hover': Colors.GRAY_50,
                'active': Colors.GRAY_100,
                'text': Colors.GRAY_700,
                'border': Colors.GRAY_300
            }
        }

        style_config = styles.get(self.style_type, styles['primary'])

        if self.style_type == 'outline':
            style = f"""
                QPushButton {{
                    background-color: {style_config['bg']};
                    color: {style_config['text']};
                    border: 2px solid {style_config['border']};
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-weight: 500;
                }}
                QPushButton:hover {{
                    background-color: {style_config['hover']};
                    border-color: {Colors.GRAY_400};
                }}
                QPushButton:pressed {{
                    background-color: {style_config['active']};
                }}
            """
        else:
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                              stop: 0 {style_config['bg']},
                                              stop: 1 {style_config['active']});
                    color: {style_config['text']};
                    border: none;
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-weight: 500;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                              stop: 0 {style_config['hover']},
                                              stop: 1 {style_config['bg']});
                }}
                QPushButton:pressed {{
                    background: {style_config['active']};
                }}
                QPushButton:disabled {{
                    background: {Colors.GRAY_300};
                    color: {Colors.GRAY_500};
                }}
            """

        self.setStyleSheet(style)


class LoadingIndicator(QWidget):
    """
    Modern loading indicator with spinner animation
    """

    def __init__(self, parent=None, size=32, color=Colors.PRIMARY):
        super().__init__(parent)

        self.size = size
        self.color = QColor(color)
        self.angle = 0

        # Set widget size
        self.setFixedSize(size, size)

        # Create timer for animation
        self.timer = QTimer()
        self.timer.timeout.connect(self.rotate)

        # Set up the widget
        self.setStyleSheet("background: transparent;")

    def start_animation(self):
        """Start the loading animation"""
        self.timer.start(50)  # Update every 50ms
        self.show()

    def stop_animation(self):
        """Stop the loading animation"""
        self.timer.stop()
        self.hide()

    def rotate(self):
        """Rotate the spinner"""
        self.angle = (self.angle + 10) % 360
        self.update()

    def paintEvent(self, event):
        """Paint the loading spinner"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Set up the pen
        pen = QPen(self.color)
        pen.setWidth(3)
        pen.setCapStyle(Qt.RoundCap)
        painter.setPen(pen)

        # Draw the spinner
        rect = QRect(3, 3, self.size - 6, self.size - 6)

        # Draw multiple arcs to create spinner effect
        for i in range(8):
            opacity = max(0.1, 1.0 - (i * 0.125))
            color = QColor(self.color)
            color.setAlphaF(opacity)
            pen.setColor(color)
            painter.setPen(pen)

            start_angle = (self.angle + i * 45) * 16  # Qt uses 1/16th degree units
            span_angle = 30 * 16  # 30 degrees

            painter.drawArc(rect, start_angle, span_angle)


class StatusIndicator(QLabel):
    """
    Status indicator with colored dot and text
    """

    def __init__(self, text="", status='neutral', parent=None):
        super().__init__(parent)

        self.status = status
        self.base_text = text

        # Set font
        font = QFont('Segoe UI', 10)
        self.setFont(font)

        # Update display
        self.update_status(status, text)

    def update_status(self, status, text=None):
        """Update the status indicator"""
        self.status = status
        if text:
            self.base_text = text

        # Status colors and icons
        status_config = {
            'success': {'color': Colors.SUCCESS, 'icon': '🟢'},
            'warning': {'color': Colors.WARNING, 'icon': '🟡'},
            'error': {'color': Colors.DANGER, 'icon': '🔴'},
            'info': {'color': Colors.INFO, 'icon': '🔵'},
            'neutral': {'color': Colors.GRAY_500, 'icon': '⚪'}
        }

        config = status_config.get(status, status_config['neutral'])

        # Set text with icon
        self.setText(f"{config['icon']} {self.base_text}")

        # Set color
        self.setStyleSheet(f"""
            QLabel {{
                color: {config['color']};
                background: transparent;
                padding: 4px 8px;
                border-radius: 4px;
            }}
        """)


class ProgressBar(QWidget):
    """
    Modern progress bar with smooth animation
    """

    def __init__(self, parent=None, height=6):
        super().__init__(parent)

        self.progress = 0
        self.height = height
        self.setFixedHeight(height)

        # Animation timer
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self.update_animation)

        # Animation properties
        self.target_progress = 0
        self.animation_speed = 2  # pixels per frame

        self.setStyleSheet("background: transparent;")

    def set_progress(self, value, animate=True):
        """Set progress value (0-100)"""
        self.target_progress = max(0, min(100, value))

        if animate:
            if not self.animation_timer.isActive():
                self.animation_timer.start(16)  # ~60 FPS
        else:
            self.progress = self.target_progress
            self.update()

    def update_animation(self):
        """Update animation frame"""
        if abs(self.progress - self.target_progress) < 1:
            self.progress = self.target_progress
            self.animation_timer.stop()
        else:
            if self.progress < self.target_progress:
                self.progress += self.animation_speed
            else:
                self.progress -= self.animation_speed

        self.update()

    def paintEvent(self, event):
        """Paint the progress bar"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Background
        bg_rect = QRect(0, 0, self.width(), self.height)
        painter.fillRect(bg_rect, QColor(Colors.GRAY_200))

        # Progress
        if self.progress > 0:
            progress_width = int((self.progress / 100) * self.width())
            progress_rect = QRect(0, 0, progress_width, self.height)

            # Gradient for progress
            gradient = QLinearGradient(0, 0, progress_width, 0)
            gradient.setColorAt(0, QColor(Colors.PRIMARY))
            gradient.setColorAt(1, QColor(Colors.PRIMARY_LIGHT))

            painter.fillRect(progress_rect, gradient)


class StyledFrame(QFrame):
    """
    PyQt5 equivalent for styled frames used throughout the application.
    Provides consistent styling and layout management.
    """
    
    def __init__(self, parent=None, bg_color='white', border_color=None, 
                 border_width=1, border_radius=0):
        super().__init__(parent)
        
        # Apply styling
        style = f"background-color: {bg_color};"
        
        if border_color:
            style += f"border: {border_width}px solid {border_color};"
        
        if border_radius > 0:
            style += f"border-radius: {border_radius}px;"
        
        self.setStyleSheet(style)


class StyledLabel(QLabel):
    """
    PyQt5 equivalent for styled labels with consistent formatting.
    """
    
    def __init__(self, text="", parent=None, font_family='Arial', font_size=12,
                 font_weight='normal', color='#2C3E50', bg_color=None):
        super().__init__(text, parent)
        
        # Set font
        font = QFont(font_family, font_size)
        if font_weight == 'bold':
            font.setBold(True)
        self.setFont(font)
        
        # Apply styling
        style = f"color: {color};"
        if bg_color:
            style += f"background-color: {bg_color};"
        
        self.setStyleSheet(style)


# Utility functions for layout management
def create_hbox_layout(*widgets, spacing=5, margins=(0, 0, 0, 0)):
    """Create a horizontal box layout with widgets"""
    layout = QHBoxLayout()
    layout.setSpacing(spacing)
    layout.setContentsMargins(*margins)
    
    for widget in widgets:
        if widget is None:
            layout.addStretch()
        else:
            layout.addWidget(widget)
    
    return layout


def create_vbox_layout(*widgets, spacing=5, margins=(0, 0, 0, 0)):
    """Create a vertical box layout with widgets"""
    layout = QVBoxLayout()
    layout.setSpacing(spacing)
    layout.setContentsMargins(*margins)
    
    for widget in widgets:
        if widget is None:
            layout.addStretch()
        else:
            layout.addWidget(widget)
    
    return layout


def create_grid_layout(widget_grid, spacing=5, margins=(0, 0, 0, 0)):
    """Create a grid layout from a 2D list of widgets"""
    layout = QGridLayout()
    layout.setSpacing(spacing)
    layout.setContentsMargins(*margins)
    
    for row, row_widgets in enumerate(widget_grid):
        for col, widget in enumerate(row_widgets):
            if widget is not None:
                layout.addWidget(widget, row, col)
    
    return layout
